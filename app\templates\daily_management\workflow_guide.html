{% extends "base.html" %}

{% block title %}工作流程指导{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .workflow-step {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        color: white;
        position: relative;
        overflow: hidden;
        transition: transform 0.3s ease;
    }

    .workflow-step:hover {
        transform: translateY(-5px);
    }

    .workflow-step::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        transition: all 0.5s;
        opacity: 0;
    }

    .workflow-step:hover::before {
        opacity: 1;
        animation: shine 0.5s ease-in-out;
    }

    @keyframes shine {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .step-number {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 15px;
    }

    .step-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .step-description {
        font-size: 1rem;
        line-height: 1.6;
        margin-bottom: 15px;
    }

    .step-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .step-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.3s;
    }

    .step-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        text-decoration: none;
        transform: scale(1.05);
    }

    .workflow-container {
        max-width: 1000px;
        margin: 0 auto;
    }

    .intro-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border-radius: 15px;
        padding: 30px;
        color: white;
        text-align: center;
        margin-bottom: 30px;
    }

    .intro-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 15px;
    }

    .intro-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 20px;
    }

    .feature-highlight {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 15px;
        margin-top: 15px;
    }

    .feature-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }

    .feature-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
        text-align: center;
        font-size: 0.9rem;
    }

    .completion-card {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border-radius: 15px;
        padding: 25px;
        color: white;
        text-align: center;
        margin-top: 30px;
    }

    .step-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .step-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    .step-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    .step-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    .step-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
    .step-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333 !important; }
    .step-7 { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: #333 !important; }
    .step-8 { background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%); }
    .step-9 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333 !important; }

    .step-6 .step-btn, .step-7 .step-btn, .step-9 .step-btn {
        background: rgba(0, 0, 0, 0.1);
        border-color: rgba(0, 0, 0, 0.2);
        color: #333;
    }

    .step-6 .step-btn:hover, .step-7 .step-btn:hover, .step-9 .step-btn:hover {
        background: rgba(0, 0, 0, 0.2);
        color: #333;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="workflow-container">
        <!-- 介绍卡片 -->
        <div class="intro-card">
            <div class="intro-title">
                <i class="fas fa-route mr-2"></i>
                食堂日常管理工作流程指导
            </div>
            <div class="intro-subtitle">
                完整的9步工作流程，帮助您高效管理食堂日常工作
            </div>
            <div class="feature-highlight">
                <strong>核心特色：</strong>
                <div class="feature-list">
                    <div class="feature-item">
                        <i class="fas fa-print mb-1"></i><br>
                        PDF报告生成
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-search mb-1"></i><br>
                        完整食品溯源
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-qrcode mb-1"></i><br>
                        QR码便捷操作
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-chart-line mb-1"></i><br>
                        数据统计分析
                    </div>
                </div>
            </div>
        </div>

        <!-- 工作流程步骤 -->
        <div class="workflow-step step-1">
            <div class="step-number">1</div>
            <div class="step-title">日常管理模块 - PDF生成</div>
            <div class="step-description">
                开始您的日常工作，记录检查、陪餐、培训等各项活动。所有记录都支持PDF打印，方便存档和上报。
            </div>
            <div class="step-actions">
                <a href="{{ url_for('daily_management.auto_inspections') }}" class="step-btn">
                    <i class="fas fa-clipboard-check mr-1"></i> 检查记录
                </a>
                <a href="{{ url_for('daily_management.auto_companions') }}" class="step-btn">
                    <i class="fas fa-user-friends mr-1"></i> 陪餐记录
                </a>
                <a href="{{ url_for('daily_management.auto_trainings') }}" class="step-btn">
                    <i class="fas fa-graduation-cap mr-1"></i> 培训记录
                </a>
            </div>
        </div>

        <div class="workflow-step step-2">
            <div class="step-number">2</div>
            <div class="step-title">供应商管理介绍</div>
            <div class="step-description">
                建立完善的供应商档案，管理供应商信息、资质证书、联系方式等，为采购工作打好基础。
            </div>
            <div class="step-actions">
                <a href="{{ url_for('supplier.index') }}" class="step-btn">
                    <i class="fas fa-truck mr-1"></i> 供应商管理
                </a>
                <a href="{{ url_for('supplier.create') }}" class="step-btn">
                    <i class="fas fa-plus mr-1"></i> 添加供应商
                </a>
            </div>
        </div>

        <div class="workflow-step step-3">
            <div class="step-number">3</div>
            <div class="step-title">食材/菜谱管理</div>
            <div class="step-description">
                管理食材库存、菜谱配方，建立标准化的菜品制作流程，确保食品质量和营养搭配。
            </div>
            <div class="step-actions">
                <a href="{{ url_for('ingredient.index') }}" class="step-btn">
                    <i class="fas fa-carrot mr-1"></i> 食材管理
                </a>
                <a href="{{ url_for('recipe.index') }}" class="step-btn">
                    <i class="fas fa-book mr-1"></i> 菜谱管理
                </a>
            </div>
        </div>

        <div class="workflow-step step-4">
            <div class="step-number">4</div>
            <div class="step-title">周菜单创建</div>
            <div class="step-description">
                制定每周菜单计划，合理搭配营养，满足师生饮食需求。支持菜单模板和快速复制功能。
            </div>
            <div class="step-actions">
                <a href="{{ url_for('weekly_menu.index') }}" class="step-btn">
                    <i class="fas fa-calendar-week mr-1"></i> 周菜单管理
                </a>
                <a href="{{ url_for('weekly_menu.create') }}" class="step-btn">
                    <i class="fas fa-plus mr-1"></i> 创建菜单
                </a>
            </div>
        </div>

        <div class="workflow-step step-5">
            <div class="step-number">5</div>
            <div class="step-title">采购订单生成</div>
            <div class="step-description">
                根据菜单计划自动生成采购订单，或手动创建采购需求。支持多供应商比价和订单管理。
            </div>
            <div class="step-actions">
                <a href="{{ url_for('purchase_order.index') }}" class="step-btn">
                    <i class="fas fa-shopping-cart mr-1"></i> 采购订单
                </a>
                <a href="{{ url_for('purchase_order.create') }}" class="step-btn">
                    <i class="fas fa-plus mr-1"></i> 创建订单
                </a>
            </div>
        </div>

        <div class="workflow-step step-6">
            <div class="step-number">6</div>
            <div class="step-title">入库详细编辑</div>
            <div class="step-description">
                详细记录货物入库信息，包括数量、质量检查、存储位置等。生成入库PDF报告，确保库存准确性。
            </div>
            <div class="step-actions">
                <a href="{{ url_for('stock_in.index') }}" class="step-btn">
                    <i class="fas fa-warehouse mr-1"></i> 入库管理
                </a>
                <a href="{{ url_for('stock_in.create') }}" class="step-btn">
                    <i class="fas fa-plus mr-1"></i> 新增入库
                </a>
            </div>
        </div>

        <div class="workflow-step step-7">
            <div class="step-number">7</div>
            <div class="step-title">消耗计划</div>
            <div class="step-description">
                制定食材消耗计划，合理安排库存使用，避免浪费和短缺。支持按菜单自动计算消耗量。
            </div>
            <div class="step-actions">
                <a href="{{ url_for('consumption_plan.index') }}" class="step-btn">
                    <i class="fas fa-chart-pie mr-1"></i> 消耗计划
                </a>
                <a href="{{ url_for('consumption_plan.create') }}" class="step-btn">
                    <i class="fas fa-plus mr-1"></i> 制定计划
                </a>
            </div>
        </div>

        <div class="workflow-step step-8">
            <div class="step-number">8</div>
            <div class="step-title">出库管理</div>
            <div class="step-description">
                记录食材出库使用情况，更新库存数量，确保库存数据的实时性和准确性。
            </div>
            <div class="step-actions">
                <a href="{{ url_for('stock_out.index') }}" class="step-btn">
                    <i class="fas fa-sign-out-alt mr-1"></i> 出库管理
                </a>
                <a href="{{ url_for('stock_out.create') }}" class="step-btn">
                    <i class="fas fa-plus mr-1"></i> 新增出库
                </a>
            </div>
        </div>

        <div class="workflow-step step-9">
            <div class="step-number">9</div>
            <div class="step-title">食品溯源和样品记录</div>
            <div class="step-description">
                完整的食品溯源体系，从采购到消费全程可追溯。留样记录确保食品安全，PDF报告支持监管要求。
            </div>
            <div class="step-actions">
                <a href="{{ url_for('traceability.index') }}" class="step-btn">
                    <i class="fas fa-search mr-1"></i> 食品溯源
                </a>
                <a href="{{ url_for('food_sample.index') }}" class="step-btn">
                    <i class="fas fa-vial mr-1"></i> 样品记录
                </a>
            </div>
        </div>

        <!-- 完成卡片 -->
        <div class="completion-card">
            <h4><i class="fas fa-trophy mr-2"></i>恭喜完成工作流程！</h4>
            <p>您已经掌握了完整的食堂管理工作流程。系统提供PDF报告和完整的食品溯源功能，帮助您高效管理食堂日常工作。</p>
            <div class="mt-3">
                <a href="{{ url_for('daily_management.index') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-home mr-1"></i> 返回日常管理首页
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    $(document).ready(function() {
        // 添加滚动动画效果
        $(window).scroll(function() {
            $('.workflow-step').each(function() {
                var elementTop = $(this).offset().top;
                var elementBottom = elementTop + $(this).outerHeight();
                var viewportTop = $(window).scrollTop();
                var viewportBottom = viewportTop + $(window).height();

                if (elementBottom > viewportTop && elementTop < viewportBottom) {
                    $(this).addClass('animate__animated animate__fadeInUp');
                }
            });
        });

        // 初始化提示工具
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
{% endblock %}
